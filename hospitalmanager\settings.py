"""
Django settings for hospitalmanager project.

Generated by 'django-admin startproject' using Django 4.2.7.

For more information on this file, see
https://docs.djangoproject.com/en/4.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/4.2/ref/settings/
"""

from pathlib import Path
import os

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/4.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = 'django-insecure-*a2y7e6$$(5nex5%&c5!m(l(zy3o7@64n@(+un74ug6f8%p$9&'

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True  # Tạm thời đặt thành True để khắc phục vấn đề static files

ALLOWED_HOSTS = ['localhost', '127.0.0.1', 'bvqy211.local', 'quanlybv.test', '*']  # Cho phép các host phổ biến


# Application definition

INSTALLED_APPS = [
    # 'django.contrib.admin',  # Disabled Django admin
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'rest_framework',
    'channels',
    'corsheaders',  # Thêm CORS headers
    'users',
    'devices',
    'api',
    'departments',
    'emails',
    'typingpractice',
    'permissions',
    'schedules',
    'reports',
    'danhmuc130',  # Thêm ứng dụng danh mục theo quyết định 130
    'danhmucbv',   # Thêm ứng dụng danh mục tại bệnh viện
    'xml4750',     # Thêm ứng dụng quản lý dữ liệu XML theo Quyết định 4750
]

MIDDLEWARE = [
    'corsheaders.middleware.CorsMiddleware',  # Thêm CORS middleware (phải đặt trước CommonMiddleware)
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

ROOT_URLCONF = 'hospitalmanager.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [BASE_DIR / 'templates'],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
                'django.template.context_processors.static',
                'permissions.context_processors.user_permissions',
                'emails.context_processors.unread_emails',
            ],
        },
    },
]

WSGI_APPLICATION = 'hospitalmanager.wsgi.application'
ASGI_APPLICATION = 'hospitalmanager.asgi.application'

# Channels settings
CHANNEL_LAYERS = {
    'default': {
        'BACKEND': 'channels.layers.InMemoryChannelLayer',
    },
}


# Database
# https://docs.djangoproject.com/en/4.2/ref/settings/#databases

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': BASE_DIR / 'db.sqlite3',
    }
}


# Password validation
# https://docs.djangoproject.com/en/4.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]


# Internationalization
# https://docs.djangoproject.com/en/4.2/topics/i18n/

LANGUAGE_CODE = 'vi'

TIME_ZONE = 'Asia/Ho_Chi_Minh'

USE_I18N = True

USE_TZ = False


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/4.2/howto/static-files/

STATIC_URL = '/static/'

# Additional locations of static files
STATICFILES_DIRS = [
    os.path.join(BASE_DIR, 'static'),
]

# Disable STATIC_ROOT for development
# STATIC_ROOT = os.path.join(BASE_DIR, 'staticfiles')

# Media files config
MEDIA_URL = '/media/'
MEDIA_ROOT = os.path.join(BASE_DIR, 'media')

# Default primary key field type
# https://docs.djangoproject.com/en/4.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# Authentication settings
LOGIN_URL = '/users/login/'
LOGIN_REDIRECT_URL = '/'
LOGOUT_REDIRECT_URL = '/users/login/'

# REST Framework settings
REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': [
        'rest_framework.authentication.SessionAuthentication',
        'api.authentication.AgentAuthentication',
    ],
    'DEFAULT_PERMISSION_CLASSES': [
        'rest_framework.permissions.IsAuthenticated',
    ],
    'DEFAULT_RENDERER_CLASSES': [
        'rest_framework.renderers.JSONRenderer',
        'rest_framework.renderers.BrowsableAPIRenderer',
    ],
}

# VNC File Transfer settings
VNC_SSH_USERNAME = 'administrator'  # Tên đăng nhập SSH mặc định
VNC_SSH_PASSWORD = 'password'       # Mật khẩu SSH mặc định
VNC_SSH_PORT = 22                   # Cổng SSH mặc định
VNC_REMOTE_DIR = 'C:\\Users\\<USER>\\Downloads'  # Thư mục đích mặc định

# Testing flag for background tasks
TESTING = False

# CORS settings
CORS_ALLOW_ALL_ORIGINS = True  # Chỉ sử dụng trong môi trường phát triển
CORS_ALLOWED_ORIGINS = [
    "http://localhost:8000",
    "http://127.0.0.1:8000",
    "http://quanlybv.test",
    "http://**********:8000",
]
CORS_ALLOW_CREDENTIALS = True
CORS_ALLOW_METHODS = [
    'DELETE',
    'GET',
    'OPTIONS',
    'PATCH',
    'POST',
    'PUT',
]
CORS_ALLOW_HEADERS = [
    'accept',
    'accept-encoding',
    'authorization',
    'content-type',
    'dnt',
    'origin',
    'user-agent',
    'x-csrftoken',
    'x-requested-with',
]

# X-Frame-Options settings
X_FRAME_OPTIONS = 'ALLOWALL'  # Cho phép nhúng trang trong iframe từ bất kỳ nguồn gốc nào

# File upload settings
# Tăng giới hạn kích thước file upload lên 100MB
DATA_UPLOAD_MAX_MEMORY_SIZE = 104857600  # 100MB
FILE_UPLOAD_MAX_MEMORY_SIZE = 104857600  # 100MB
# Thời gian timeout cho upload (giây)
DATA_UPLOAD_MAX_NUMBER_FIELDS = 10000