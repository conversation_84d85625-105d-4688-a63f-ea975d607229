/**
 * tabulator_xml_tables.js
 * <PERSON><PERSON><PERSON> các định nghĩa bảng và cấu hình cột cho module XML4750
 */

// Hàm tạo cấu hình cột cho Tabulator
function getColumnsConfig(xmlType) {
    var columns = [];

    switch(xmlType) {
        case 'XML0':
            columns = columns.concat([
                { title:"Chọn", formatter:"rowSelection", titleFormatter:"rowSelection", hozAlign:"center", headerSort:false, frozen:true, width:40},
                { title: "Mã liên kết", field: "maLK", headerFilter: true, headerVertical: false, frozen:true},
                { title: "Mã bệnh nhân", field: "maBN",headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Họ tên", field: "hoTen", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Số CCCD", field: "soCCCD", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "<PERSON><PERSON><PERSON> sinh", field: "ngaySinh", headerFilter: true, headerVertical: false, editor: dateTimeEditor, formatter: formatDateTime },
                {
                    title: "Giới tính", field: "gioiTinh", headerFilter: true, headerVertical: false, editor: "list",
                    editorParams: {
                        values: Object.entries(gioi_tinh).map(([key, label]) => ({ value: key,label: `${key} - ${label}`})),
                    },
                    formatter: formatKeyValue(gioi_tinh),
                },
                { title: "Mã thẻ BHYT", field: "maTheBHYT", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã ĐKBD", field: "maDKBD", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Giá trị thẻ từ", field: "gtTheTu", headerFilter: true, headerVertical: false, editor: dateEditor, formatter: formatDate },
                { title: "Giá trị thẻ đến", field: "gtTheDen", headerFilter: true, headerVertical: false, editor: dateEditor, formatter: formatDate},
                {
                    title: "Mã đối tượng KCB", field: "maDoiTuongKCB", headerFilter: true, headerVertical: false,
                    editor: "list",
                    editorParams: {
                        // Sử dụng dữ liệu đã load sẵn
                        values: getCategoryValues("doituongkcb"),
                        placeholderEmpty:"Không có kết quả", //set custom placeholder when list is empty
                        autocomplete:true, //enable autocomplete mode,
                        listOnEmpty:true, //show all values in the list if the input is empty
                        filterDelay:100, //delay in milliseconds after typing before filter begins
                        clearable:true, //show clear "x" button on editor
                    },
                    // Thêm formatter để hiển thị tên đầy đủ thay vì mã
                    formatter: function(cell) {
                        var value = cell.getValue();
                        var values = getCategoryValues("doituongkcb");
                        return values[value] || value;
                    }
                },
                { title: "Ngày vào", field: "ngayVao", headerFilter: true, headerVertical: false, editor: dateTimeEditor, formatter: formatDateTime },
                { title: "Ngày vào nội trú", field: "ngayVaoNoiTru", headerFilter: true, headerVertical: false, editor: dateTimeEditor, formatter: formatDateTime },
                { title: "Lý do vào nội trú", field: "ly_do_vnt", headerFilter: true, headerVertical: false, editor:"input"},
                {
                    title: "Mã lý do vào nội trú", field: "ma_ly_do_vnt", headerFilter: true, headerVertical: false, editor:"list",
                    editorParams: {
                        // Sử dụng dữ liệu đã load sẵn
                        values: Object.entries(ma_doi_tuong).map(([key, label]) => ({ value: key,label: `${key} - ${label}`})),
                        placeholderEmpty:"Không có kết quả", //set custom placeholder when list is empty
                        autocomplete:true, //enable autocomplete mode,
                        listOnEmpty:true, //show all values in the list if the input is empty
                        filterDelay:100, //delay in milliseconds after typing before filter begins
                        clearable:true, //
                    },
                    // Thêm formatter để hiển thị tên đầy đủ cả mã và tên
                    formatter: formatKeyValue(ma_doi_tuong),
                },
                { title: "Mã loại KCB", field: "maLoaiKCB", headerFilter: true, headerVertical: false, editor:"list",
                    editorParams: {
                        // Sử dụng dữ liệu đã load sẵn
                        values: Object.entries(ma_loai_kcb).map(([key, label]) => ({ value: key,label: `${key} - ${label}`})),
                        placeholderEmpty:"Không có kết quả", //set custom placeholder when list is empty
                        autocomplete:true, //enable autocomplete mode,
                        listOnEmpty:true, //show all values in the list if the input is empty
                        filterDelay:100, //delay in milliseconds after typing before filter begins
                        clearable:true, //show clear "x" button on editor
                    },
                    // Thêm formatter để hiển thị tên đầy đủ cả mã và tên
                    formatter: formatKeyValue(ma_loai_kcb),
                },
                { title: "Mã CSKCB", field: "maCSKCB", headerFilter: true, headerVertical: false, editor:"input"  },
                { title: "Mã dịch vụ", field: "maDichVu", headerFilter: true, headerVertical: false, editor:"input"  },
                { title: "Tên dịch vụ", field: "tenDichVu", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã thuốc", field: "ma_thuoc", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Tên thuốc", field: "tenThuoc", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã vật tư", field: "maVatTu", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Tên vật tư", field: "tenVatTu", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Ngày y lệnh", field: "ngayYL", headerFilter: true, headerVertical: false, editor: dateTimeEditor, formatter: formatDateTime },
                { title: "Dự phòng", field: "duPhong", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Ngày tạo", field: "ngayTao", headerFilter: true, headerVertical: false,  editor: dateTimeEditor, formatter: formatDateTime },
                { title: "Ngày chỉnh sửa", field: "ngayChinhSua", headerFilter: true, headerVertical: false, editor: dateTimeEditor, formatter: formatDateTime },
                { title: "TT gửi BHXH", field: "trangThaiGuiBHXH", headerFilter: true, headerVertical: false, editor:"input" },
            ]);
            break;

        case 'XML1':
            columns = columns.concat([
                { title:"Chọn", formatter:"rowSelection", titleFormatter:"rowSelection", hozAlign:"center", headerSort:false, frozen:true, width:40},
                { title: "Mã liên kết", field: "maLK", headerFilter: true, headerVertical: false, frozen:true },
                { title: "Mã bệnh nhân", field: "maBN", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Họ tên", field: "hoTen", width: 180, headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Số CCCD", field: "soCCCD", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Ngày sinh", field: "ngaySinh", headerFilter: true, headerVertical: false, editor: dateTimeEditor, formatter: formatDateTime },
                {
                    title: "Giới tính", field: "gioiTinh", headerFilter: true, headerVertical: false, editor: "list",
                    editorParams: { values: Object.entries(gioi_tinh).map(([key, label]) => ({ value: key,label: `${key} - ${label}`}))},
                    formatter: formatKeyValue(gioi_tinh),
                },
                { title: "Nhóm máu", field: "nhomMau", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã quốc tịch", field: "maQuocTich", headerFilter: true, headerVertical: false, editor:"input" },
                {
                    title: "Mã dân tộc", field: "maDanToc", headerFilter: true, headerVertical: false, editor:"list",
                    editorParams: {
                        // Sử dụng dữ liệu đã load sẵn
                        values: Object.entries(ma_dan_toc).map(([key, label]) => ({ value: key,label: `${key} - ${label}`})),
                        placeholderEmpty:"Không có kết quả", //set custom placeholder when list is empty
                        autocomplete:true, //enable autocomplete mode,
                        listOnEmpty:true, //show all values in the list if the input is empty
                        filterDelay:100, //delay in milliseconds after typing before filter begins
                        clearable:true, //show clear "x" button on editor
                    },
                    // Thêm formatter để hiển thị tên đầy đủ cả mã và tên
                    formatter: formatKeyValue(ma_dan_toc),
                },
                { title: "Mã nghề nghiệp", field: "maNgheNghiep", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Địa chỉ", field: "diaChi", width: 250, headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã tỉnh cư trú", field: "maTinhCuTru", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã huyện cư trú", field: "maHuyenCuTru", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã xã cư trú", field: "maXaCuTru", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Số điện thoại", field: "dienThoai", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã thẻ BHYT", field: "maTheBHYT", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã ĐKBD", field: "maDKBD", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Giá trị thẻ từ", field: "gtTheTu", headerFilter: true, headerVertical: false, editor: dateEditor, formatter: formatDate },
                { title: "Giá trị thẻ đến", field: "gtTheDen", headerFilter: true, headerVertical: false, editor: dateEditor, formatter: formatDate },
                { title: "Ngày miễn CCT", field: "ngayMienCCT", headerFilter: true, headerVertical: false, editor: dateEditor, formatter: formatDate },
                { title: "Lý do vào viện", field: "lyDoVV", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Lý do vào nội trú", field: "lyDoVNT", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã lý do vào nội trú", field: "maLyDoVNT", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Chẩn đoán vào", field: "chanDoanVao", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Chẩn đoán ra viện", field: "chanDoanRV", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã bệnh chính", field: "maBenhChinh", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã bệnh kèm theo", field: "maBenhKT", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã bệnh YHCT", field: "maBenhmaBenhYHCTKem2", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã PTTT QT", field: "maPTTTQT", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã đối tượng KCB", field: "maDoiTuongKCB", headerFilter: true, headerVertical: false, editor: "input" },
                { title: "Mã nơi đi", field: "maNoiDi", headerFilter: true, headerVertical: false, editor: "input" },
                { title: "Mã nơi đến", field: "maNoiDen", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã tai nạn", field: "maTaiNan", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Ngày vào", field: "ngayVao", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Giấy chuyển tuyến", field: "giayChuyenTuyen", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Ngày ĐT", field: "soNgayDtri", headerFilter: true, headerVertical: false, editor: "input"},
                { title: "PP ĐT", field: "ppDieuTri", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "KQ ĐT", field: "ketQuaDtri", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã loại ra viện", field: "maLoaiRV", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Ghi chú", field: "ghiChu", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Ngày thanh toán", field: "ngayTToan", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Tổng chi thuốc", field: "tThuoc", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Tổng chi VTYT", field: "tVTYT", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Tổng chi BV", field: "tTongChiBV", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Tổng chi BH", field: "tTongChiBH", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "BNTT", field: "tBNTT", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "BNCCT", field: "tBNCCT", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "BHTT", field: "tBHTT", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Nguồn khác", field: "tNguonKhac", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "BHTT GDV", field: "tBHTTGDV", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Năm QT", field: "namQT", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Tháng QT", field: "thangQT", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã loại KCB", field: "maLoaiKCB", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã khoa", field: "maKhoa", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã CSKCB", field: "maCSKCB", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã khu vực", field: "maKhuVuc", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Cân nặng", field: "canNang", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Cân nặng con", field: "canNangCon", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "5 năm LT", field: "namNamLienTuc", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Ngày tái khám", field: "ngayTaiKham", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã HSBA", field: "maHSBA", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã TTDV", field: "maTTDV", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Dự phòng", field: "duPhong", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Ngày tạo", field: "ngayTao", headerFilter: true, headerVertical: false,  editor: dateTimeEditor, formatter: formatDateTime },
                { title: "Ngày chỉnh sửa", field: "ngayChinhSua", headerFilter: true, headerVertical: false, editor: dateTimeEditor, formatter: formatDateTime },
                { title: "TT gửi BHXH", field: "trangThaiGuiBHXH", headerFilter: true, headerVertical: false, editor:"input" },
            ]);
            break;

        case 'XML2':
            columns = columns.concat([
                { title:"Chọn", formatter:"rowSelection", titleFormatter:"rowSelection", hozAlign:"center", headerSort:false, frozen:true, width:40},
                { title: "Mã liên kết", field: "maLK", headerFilter: true, headerVertical: false, frozen:true },
                { title: "STT", field: "stt", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã thuốc", field: "maThuoc", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã PP chế biến", field: "maPPCheBien", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã CSKCB thuốc", field: "maCSKCBThuoc", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã nhóm", field: "maNhom", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Tên thuốc", field: "tenThuoc", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Đơn vị tính", field: "donViTinh", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Hàm lượng", field: "hamLuong", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Đường dùng", field: "duongDung", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Dạng bào chế", field: "dangBaoChe", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Liều dùng", field: "lieuDung", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Cách dùng", field: "cachDung", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Số đăng ký", field: "soDangKy", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "TT thầu", field: "ttThau", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Phạm vi", field: "phamVi", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Tỷ lệ TT BH", field: "tyLeTTBH", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Số lượng", field: "soLuong", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Đơn giá", field: "donGia", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Thành tiền BV", field: "thanhTienBV", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Thành tiền BH", field: "thanhTienBH", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Nguồn NSNN", field: "tNguonKhacNSNN", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Nguồn VTNN", field: "tNguonKhacVTNN", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Nguồn VTTN", field: "tNguonKhacVTTN", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Nguồn còn lại", field: "tNguonKhacCL", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Nguồn khác", field: "tNguonKhac", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mức hưởng", field: "mucHuong", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "BN thanh toán", field: "tBNTT", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "BN cùng chi trả", field: "tBNCCT", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "BH thanh toán", field: "tBHTT", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã khoa", field: "maKhoa", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã bác sĩ", field: "maBacSi", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã dịch vụ", field: "maDichVu", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Ngày y lệnh", field: "ngayYL", headerFilter: true, headerVertical: false, editor: dateTimeEditor, formatter: formatDateTime },
                { title: "Ngày TH y lệnh", field: "ngayTHYL", headerFilter: true, headerVertical: false, editor: dateTimeEditor, formatter: formatDateTime },
                { title: "Mã PTTT", field: "maPTTT", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Nguồn chi trả", field: "nguonCTra", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Vết thương TP", field: "vetThuongTP", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Dự phòng", field: "duPhong", headerFilter: true, headerVertical: false, editor:"input" }
            ]);
            break;

        case 'XML3':
            columns = columns.concat([
                { title:"Chọn", formatter:"rowSelection", titleFormatter:"rowSelection", hozAlign:"center", headerSort:false, frozen:true, width:40},
                { title: "Mã liên kết", field: "maLK", headerFilter: true, headerVertical: false, frozen:true },
                { title: "STT", field: "stt", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã dịch vụ", field: "maDichVu", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã PTTT QT", field: "maPTTTQT", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã vật tư", field: "maVatTu", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã nhóm", field: "maNhom", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Gói VTYT", field: "goiVTYT", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Tên vật tư", field: "tenVatTu", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Tên dịch vụ", field: "tenDichVu", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã xăng dầu", field: "maXangDau", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Đơn vị tính", field: "donViTinh", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Phạm vi", field: "phamVi", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Số lượng", field: "soLuong", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Đơn giá BV", field: "donGiaBV", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Đơn giá BH", field: "donGiaBH", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Thành tiền BV", field: "thanhTienBV", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Thành tiền BH", field: "thanhTienBH", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "TT thầu", field: "ttThau", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Tỷ lệ TT DV", field: "tyLeTTDV", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Tỷ lệ TT BH", field: "tyLeTTBH", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Trần TT", field: "tTranTT", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mức hưởng", field: "mucHuong", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Nguồn NSNN", field: "tNguonKhacNSNN", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Nguồn VTNN", field: "tNguonKhacVTNN", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Nguồn VTTN", field: "tNguonKhacVTTN", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Nguồn còn lại", field: "tNguonKhacCL", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Nguồn khác", field: "tNguonKhac", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "BN thanh toán", field: "tBNTT", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "BN cùng chi trả", field: "tBNCCT", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "BH thanh toán", field: "tBHTT", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã khoa", field: "maKhoa", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã giường", field: "maGiuong", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã bác sĩ", field: "maBacSi", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Người thực hiện", field: "nguoiThucHien", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã bệnh", field: "maBenh", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã bệnh YHCT", field: "maBenhYHCT", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Ngày y lệnh", field: "ngayYL", headerFilter: true, headerVertical: false, editor: dateTimeEditor, formatter: formatDateTime },
                { title: "Ngày TH y lệnh", field: "ngayTHYL", headerFilter: true, headerVertical: false, editor: dateTimeEditor, formatter: formatDateTime },
                { title: "Ngày kết quả", field: "ngayKQ", headerFilter: true, headerVertical: false, editor: dateTimeEditor, formatter: formatDateTime },
                { title: "Mã PTTT", field: "maPTTT", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Vết thương TP", field: "vetThuongTP", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "PP vô cảm", field: "ppVoCam", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Vị trí TH DVKT", field: "viTriThDVKT", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã máy", field: "maMay", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã hiệu SP", field: "maHieuSP", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Tái sử dụng", field: "taiSuDung", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Dự phòng", field: "duPhong", headerFilter: true, headerVertical: false, editor:"input" }
            ]);
            break;

        case 'XML4':
            columns = columns.concat([
                { title:"Chọn", formatter:"rowSelection", titleFormatter:"rowSelection", hozAlign:"center", headerSort:false, frozen:true, width:40},
                { title: "Mã liên kết", field: "maLK", headerFilter: true, headerVertical: false, frozen:true },
                { title: "STT", field: "stt", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã dịch vụ", field: "maDichVu", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã chỉ số", field: "maChiSo", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Tên chỉ số", field: "tenChiSo", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Giá trị", field: "giaTri", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Đơn vị đo", field: "donViDo", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mô tả", field: "moTa", width: 400, headerFilter: true, headerVertical: false, editor:"textarea", variableHeight: true },
                { title: "Kết luận", field: "ketLuan", headerFilter: true, headerVertical: false, editor:"textarea", variableHeight: true },
                { title: "Ngày kết quả", field: "ngayKQ", headerFilter: true, headerVertical: false, editor: dateTimeEditor, formatter: formatDateTime },
                { title: "Mã BS đọc KQ", field: "maBSDocKQ", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Dự phòng", field: "duPhong", headerFilter: true, headerVertical: false, editor:"input" }
            ]);
            break;

        case 'XML5':
            columns = columns.concat([
                { title:"Chọn", formatter:"rowSelection", titleFormatter:"rowSelection", hozAlign:"center", headerSort:false, frozen:true, width:40},
                { title: "Mã liên kết", field: "maLK", headerFilter: true, headerVertical: false, frozen:true },
                { title: "STT", field: "stt", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Diễn biến LS", field: "dienBienLS", width: 300, headerFilter: true, headerVertical: false, editor:"textarea" },
                { title: "Giai đoạn bệnh", field: "giaiDoanBenh", width: 300, headerFilter: true, headerVertical: false, editor:"textarea" },
                { title: "Hội chẩn", field: "hoiChan", width: 300, headerFilter: true, headerVertical: false, editor:"textarea" },
                { title: "Phẫu thuật", field: "phauThuat", width: 300, headerFilter: true, headerVertical: false, editor:"textarea" },
                { title: "Thời điểm DBLS", field: "thoiDiemDBLS", headerFilter: true, headerVertical: false, editor: dateTimeEditor, formatter: formatDateTime },
                { title: "Người thực hiện", field: "nguoiThucHien", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Dự phòng", field: "duPhong", headerFilter: true, headerVertical: false, editor:"input" }
            ]);
            break;

        case 'XML6':
            columns = columns.concat([
                { title:"Chọn", formatter:"rowSelection", titleFormatter:"rowSelection", hozAlign:"center", headerSort:false, frozen:true, width:40},
                { title: "Mã liên kết", field: "maLK", headerFilter: true, headerVertical: false, frozen:true },
                { title: "Mã thẻ BHYT", field: "maTheBHYT", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Số CCCD", field: "soCCCD", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Ngày sinh", field: "ngaySinh", headerFilter: true, headerVertical: false, editor: dateEditor, formatter: formatDate },
                { title: "Giới tính", field: "gioiTinh", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Địa chỉ", field: "diaChi", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã tỉnh cư trú", field: "maTinhCuTru", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã huyện cư trú", field: "maHuyenCuTru", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã xã cư trú", field: "maXaCuTru", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Ngày khẳng định HIV", field: "ngayKDHIV", headerFilter: true, headerVertical: false, editor: dateEditor, formatter: formatDate },
                { title: "Nơi lấy mẫu XN", field: "noiLayMauXN", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Nơi XN khẳng định", field: "noiXNKD", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Nơi bắt đầu điều trị ARV", field: "noiBDDTARV", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Bắt đầu điều trị ARV", field: "bdDTARV", headerFilter: true, headerVertical: false, editor: dateEditor, formatter: formatDate },
                { title: "Mã phác đồ điều trị ban đầu", field: "maPhacDoDieuTriBD", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã bậc phác đồ ban đầu", field: "maBacPhacDoBD", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã lý do điều trị", field: "maLydoDtri", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Loại điều trị lao", field: "loaiDtriLao", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Sàng lọc lao", field: "sangLocLao", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Phác đồ điều trị lao", field: "phacDoDtriLao", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Ngày bắt đầu điều trị lao", field: "ngayBDDTriLao", headerFilter: true, headerVertical: false, editor: dateEditor, formatter: formatDate },
                { title: "Ngày kết thúc điều trị lao", field: "ngayKTDTriLao", headerFilter: true, headerVertical: false, editor: dateEditor, formatter: formatDate },
                { title: "Kết quả điều trị lao", field: "ketQuaDTriLao", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã lý do xét nghiệm TLVR", field: "maLydoXNTLVR", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Ngày xét nghiệm TLVR", field: "ngayXNTLVR", headerFilter: true, headerVertical: false, editor: dateEditor, formatter: formatDate },
                { title: "Kết quả xét nghiệm TLVR", field: "kqXNTLVR", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Ngày kết quả xét nghiệm TLVR", field: "ngayKQXNTLVR", headerFilter: true, headerVertical: false, editor: dateEditor, formatter: formatDate },
                { title: "Mã loại bệnh nhân", field: "maLoaiBN", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Giai đoạn làm sàng", field: "giaiDoanLamSang", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Nhóm đối tượng", field: "nhomDoiTuong", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã tình trạng đăng ký", field: "maTinhTrangDK", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Lần xét nghiệm PCR", field: "lanXNPCR", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Ngày xét nghiệm PCR", field: "ngayXNPCR", headerFilter: true, headerVertical: false, editor: dateEditor, formatter: formatDate },
                { title: "Ngày kết quả xét nghiệm PCR", field: "ngayKQXNPCR", headerFilter: true, headerVertical: false, editor: dateEditor, formatter: formatDate },
                { title: "Mã kết quả xét nghiệm PCR", field: "maKQXNPCR", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Ngày nhận thông tin mang thai", field: "ngayNhanTTMangThai", headerFilter: true, headerVertical: false, editor: dateEditor, formatter: formatDate },
                { title: "Ngày bắt đầu điều trị ctx", field: "ngayBatDauDTCTX", headerFilter: true, headerVertical: false, editor: dateEditor, formatter: formatDate },
                { title: "Mã xử trí", field: "maXuTri", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Ngày bắt đầu xử trí", field: "ngayBatDauXuTri", headerFilter: true, headerVertical: false, editor: dateEditor, formatter: formatDate },
                { title: "Ngày kết thúc xử trí", field: "ngayKetThucXuTri", headerFilter: true, headerVertical: false, editor: dateEditor, formatter: formatDate },
                { title: "Mã phác đồ điều trị", field: "maPhacDoDieuTri", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã bậc phác đồ", field: "maBacPhacDo", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Số ngày cấp thuốc ARV", field: "soNgayCapThuocARV", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Ngày chuyển phác đồ", field: "ngayChuyenPhacDo", headerFilter: true, headerVertical: false, editor: dateEditor, formatter: formatDate },
                { title: "Lý do chuyển phác đồ", field: "lyDoChuyenPhacDo", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã cơ sở khám chữa bệnh", field: "maCSKCB", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Dự phòng", field: "duPhong", headerFilter: true, headerVertical: false, editor:"input" }
            ]);
            break;

        case 'XML7':
            columns = columns.concat([
                { title:"Chọn", formatter:"rowSelection", titleFormatter:"rowSelection", hozAlign:"center", headerSort:false, frozen:true, width:40},
                { title: "Mã liên kết", field: "maLK", headerFilter: true, headerVertical: false, frozen:true },
                { title: "Số lưu trữ", field: "soLuuTru", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã y tế", field: "maYTe", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã khoa ra viện", field: "maKhoaRV", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Ngày vào", field: "ngayVao", headerFilter: true, headerVertical: false, editor: dateEditor, formatter: formatDateTime },
                { title: "Ngày ra", field: "ngayRa", headerFilter: true, headerVertical: false, editor: dateEditor, formatter: formatDateTime },
                { title: "Mã định chỉ thai", field: "maDinhChiThai", headerFilter: true, headerVertical: false, editor:"number" },
                { title: "Nguyên nhân định chỉ", field: "nguyenNhanDinhChi", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Thời gian định chỉ", field: "thoiGianDinhChi", headerFilter: true, headerVertical: false, editor: dateEditor, formatter: formatDate },
                { title: "Tuổi thai", field: "tuoiThai", headerFilter: true, headerVertical: false, editor:"number" },
                { title: "Chẩn đoán ra viện", field: "chanDoanRV", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Phương pháp điều trị", field: "ppDieuTri", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Ghi chú", field: "ghiChu", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã Thủ trưởng đơn vị", field: "maTTDV", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã Bác sĩ", field: "maBS", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Tên Bác sĩ", field: "tenBS", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Ngày chứng từ", field: "ngayCT", headerFilter: true, headerVertical: false, editor: dateEditor, formatter: formatDate },
                { title: "Mã cha", field: "maCha", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã mẹ", field: "maMe", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã thẻ tạm", field: "maTheTam", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Họ tên cha", field: "hoTenCha", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Họ tên mẹ", field: "hoTenMe", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Số ngày nghỉ", field: "soNgayNghi", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Ngoại trú từ ngày", field: "ngoaiTruTuNgay", headerFilter: true, headerVertical: false, editor: dateEditor, formatter: formatDate },
                { title: "Ngoại trú đến ngày", field: "ngoaiTruDenNgay", headerFilter: true, headerVertical: false, editor: dateEditor, formatter: formatDate },
                { title: "Dự phòng", field: "duPhong", headerFilter: true, headerVertical: false, editor:"input" }
            ]);
            break;

        case 'XML8':
            columns = columns.concat([
                { title:"Chọn", formatter:"rowSelection", titleFormatter:"rowSelection", hozAlign:"center", headerSort:false, frozen:true, width:40},
                { title: "Mã liên kết", field: "maLK", headerFilter: true, headerVertical: false, frozen:true },
                { title: "Mã loại KCB", field: "maLoaiKCB", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Họ tên cha", field: "hoTenCha", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Họ tên mẹ", field: "hoTenMe", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Người giám hộ", field: "nguoiGiamHo", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Đơn vị", field: "donVi", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Ngày vào", field: "ngayVao", headerFilter: true, headerVertical: false, editor: dateEditor, formatter: formatDate },
                { title: "Ngày ra", field: "ngayRa", headerFilter: true, headerVertical: false, editor: dateEditor, formatter: formatDate },
                { title: "Chẩn đoán vào", field: "chanDoanVao", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Chẩn đoán ra viện", field: "chanDoanRV", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Quá trình bệnh lý", field: "qtBenhLy", headerFilter: true, headerVertical: false, editor:"textarea" },
                { title: "Tóm tắt kết quả", field: "tomTatKQ", headerFilter: true, headerVertical: false, editor:"textarea" },
                { title: "Phương pháp điều trị", field: "ppDieuTri", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Ngày sinh con", field: "ngaySinhCon", headerFilter: true, headerVertical: false, editor: dateEditor, formatter: formatDate },
                { title: "Ngày con chết", field: "ngayConChet", headerFilter: true, headerVertical: false, editor: dateEditor, formatter: formatDate },
                { title: "Số con chết", field: "soConChet", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Kết quả điều trị", field: "ketQuaDtri", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Ghi chú", field: "ghiChu", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã Thủ trưởng đơn vị", field: "maTTDV", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Ngày chứng từ", field: "ngayCT", headerFilter: true, headerVertical: false, editor: dateEditor, formatter: formatDate },
                { title: "Mã thẻ tạm", field: "maTheTam", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Dự phòng", field: "duPhong", headerFilter: true, headerVertical: false, editor:"input" }
            ]);
            break;

        case 'XML9':
            columns = columns.concat([
                { title:"Chọn", formatter:"rowSelection", titleFormatter:"rowSelection", hozAlign:"center", headerSort:false, frozen:true, width:40},
                { title: "Mã liên kết", field: "maLK", headerFilter: true, headerVertical: false, frozen:true },
                { title: "Mã BHXH NND", field: "maBHXHNND", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã thẻ NND", field: "maTheNND", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Họ tên NND", field: "hoTenNND", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Ngày sinh NND", field: "ngaySinhNND", headerFilter: true, headerVertical: false, editor: dateEditor, formatter: formatDate },
                { title: "Mã dân tộc NND", field: "maDanTocNND", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Số CCCD NND", field: "soCCCDNND", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Ngày cấp CCCD NND", field: "ngayCapCCCDNND", headerFilter: true, headerVertical: false, editor: dateEditor, formatter: formatDate },
                { title: "Nơi cấp CCCD NND", field: "noiCapCCCDNND", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Nơi cư trú NND", field: "noiCuTruNND", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã quốc tịch", field: "maQuocTich", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã tỉnh cư trú", field: "maTinhCuTru", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã huyện cư trú", field: "maHuyenCuTru", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã xã cư trú", field: "maXaCuTru", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Họ tên cha", field: "hoTenCha", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã thẻ tạm", field: "maTheTam", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Họ tên con", field: "hoTenCon", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Giới tính con", field: "gioiTinhCon", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Số con", field: "soCon", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Lần sinh", field: "lanSinh", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Số con sống", field: "soConSong", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Cân nặng con", field: "canNangCon", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Ngày sinh con", field: "ngaySinhCon", headerFilter: true, headerVertical: false, editor: dateEditor, formatter: formatDate },
                { title: "Nơi sinh con", field: "noiSinhCon", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Tình trạng con", field: "tinhTrangCon", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Sinh con phẫu thuật", field: "sinhConPhauThuat", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Sinh con dưới 32 tuần", field: "sinhConDuoi32Tuan", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Ghi chú", field: "ghiChu", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Người đỡ đẻ", field: "nguoiDoDe", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Người ghi phiếu", field: "nguoiGhiPhieu", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Số chứng từ", field: "so", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Quyển số", field: "quyenSo", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Ngày chứng từ", field: "ngayCT", headerFilter: true, headerVertical: false, editor: dateEditor, formatter: formatDate },
                { title: "Mã Thủ trưởng đơn vị", field: "maTTDV", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Dự phòng", field: "duPhong", headerFilter: true, headerVertical: false, editor:"input" }
            ]);
            break;

        case 'XML10':
            columns = columns.concat([
                { title:"Chọn", formatter:"rowSelection", titleFormatter:"rowSelection", hozAlign:"center", headerSort:false, frozen:true, width:40},
                { title: "Mã liên kết", field: "maLK", headerFilter: true, headerVertical: false, frozen:true },
                { title: "Số seri", field: "soSeri", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Số chứng từ", field: "soCT", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Số ngày", field: "soNgay", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Đơn vị", field: "donVi", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Chẩn đoán ra viện", field: "chanDoanRV", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Từ ngày", field: "tuNgay", headerFilter: true, headerVertical: false, editor: dateEditor, formatter: formatDate },
                { title: "Đến ngày", field: "denNgay", headerFilter: true, headerVertical: false, editor: dateEditor, formatter: formatDate },
                { title: "Mã Thủ trưởng đơn vị", field: "maTTDV", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Tên bác sĩ", field: "tenBS", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã bác sĩ", field: "maBS", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Ngày chứng từ", field: "ngayCT", headerFilter: true, headerVertical: false, editor: dateEditor, formatter: formatDate },
                { title: "Dự phòng", field: "duPhong", headerFilter: true, headerVertical: false, editor:"input" }
            ]);
            break;

        case 'XML11':
            columns = columns.concat([
                { title:"Chọn", formatter:"rowSelection", titleFormatter:"rowSelection", hozAlign:"center", headerSort:false, frozen:true, width:40},
                { title: "Mã liên kết", field: "maLK", headerFilter: true, headerVertical: false, frozen:true },
                { title: "Số chứng từ", field: "soCT", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Số seri", field: "soSeri", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Số KCB", field: "soKCB", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Đơn vị", field: "donVi", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã BHXH", field: "maBHXH", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã thẻ BHYT", field: "maTheBHYT", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Chẩn đoán ra viện", field: "chanDoanRV", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Phương pháp điều trị", field: "ppDieuTri", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã định chỉ thai", field: "maDinhChiThai", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Nguyên nhân định chỉ", field: "nguyenNhanDinhChi", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Tuổi thai", field: "tuoiThai", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Số ngày nghỉ", field: "soNgayNghi", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Từ ngày", field: "tuNgay", headerFilter: true, headerVertical: false, editor: dateEditor, formatter: formatDate },
                { title: "Đến ngày", field: "denNgay", headerFilter: true, headerVertical: false, editor: dateEditor, formatter: formatDate },
                { title: "Họ tên cha", field: "hoTenCha", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Họ tên mẹ", field: "hoTenMe", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã Thủ trưởng đơn vị", field: "maTTDV", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã bác sĩ", field: "maBS", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Ngày chứng từ", field: "ngayCT", headerFilter: true, headerVertical: false, editor: dateEditor, formatter: formatDate },
                { title: "Mã thẻ tạm", field: "maTheTam", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mẫu số", field: "mauSo", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Dự phòng", field: "duPhong", headerFilter: true, headerVertical: false, editor:"input" }
            ]);
            break;

        case 'XML12':
            columns = columns.concat([
                { title:"Chọn", formatter:"rowSelection", titleFormatter:"rowSelection", hozAlign:"center", headerSort:false, frozen:true, width:40},
                { title: "Người chủ trì", field: "nguoiChuTri", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Chức vụ", field: "chucVu", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Ngày họp", field: "ngayHop", headerFilter: true, headerVertical: false, editor: dateEditor, formatter: formatDate },
                { title: "Họ tên", field: "hoTen", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Ngày sinh", field: "ngaySinh", headerFilter: true, headerVertical: false, editor: dateEditor, formatter: formatDate },
                { title: "Số CCCD", field: "soCCCD", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Ngày cấp CCCD", field: "ngayCapCCCD", headerFilter: true, headerVertical: false, editor: dateEditor, formatter: formatDate },
                { title: "Nơi cấp CCCD", field: "noiCapCCCD", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Địa chỉ", field: "diaChi", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã tỉnh cư trú", field: "maTinhCuTru", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã huyện cư trú", field: "maHuyenCuTru", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã xã cư trú", field: "maXaCuTru", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã BHXH", field: "maBHXH", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã thẻ BHYT", field: "maTheBHYT", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Nghề nghiệp", field: "ngheNghiep", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Điện thoại", field: "dienThoai", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã đối tượng", field: "maDoiTuong", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Khám giám định", field: "khamGiamDinh", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Số biên bản", field: "soBienBan", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Tỷ lệ thương tật cũ (%)", field: "tyLeTTCTCu", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Dạng hướng chế độ", field: "dangHuongCheDo", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Ngày chứng từ", field: "ngayChungTu", headerFilter: true, headerVertical: false, editor: dateEditor, formatter: formatDate },
                { title: "Số giấy giới thiệu", field: "soGiayGioiThieu", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Ngày đề nghị", field: "ngayDeNghi", headerFilter: true, headerVertical: false, editor: dateEditor, formatter: formatDate },
                { title: "Mã đơn vị", field: "maDonVi", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Giới thiệu của", field: "gioiThieuCua", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Kết quả khám", field: "ketQuaKham", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Số văn bản căn cứ", field: "soVanBanCanCu", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Tỷ lệ thương tật mới (%)", field: "tyLeTTCTMoi", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Tổng tỷ lệ thương tật (%)", field: "tongTyLeTTCT", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Dạng khuyết tật", field: "dangkhuyettat", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mức độ khuyết tật", field: "mucDoKhuyetTat", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Đề nghị", field: "deNghi", width: 400, headerFilter: true, headerVertical: false, editor:"textarea", variableHeight: true },
                { title: "Được xác định", field: "duocXacDinh", width: 400, headerFilter: true, headerVertical: false, editor:"textarea", variableHeight: true },
                { title: "Dự phòng", field: "duPhong", headerFilter: true, headerVertical: false, editor:"input" },
            ]);
            break;

        case 'XML13':
            columns = columns.concat([
                { title:"Chọn", formatter:"rowSelection", titleFormatter:"rowSelection", hozAlign:"center", headerSort:false, frozen:true, width:40},
                { title: "Mã liên kết", field: "maLK", headerFilter: true, headerVertical: false, frozen:true },
                { title: "Số hồ sơ", field: "soHoSo", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Số chuyển tuyến", field: "soChuyenTuyen", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Giấy chuyển tuyến", field: "giayChuyenTuyen", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã CSKCB", field: "maCSKCB", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã nơi đi", field: "maNoiDi", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã nơi đến", field: "maNoiDen", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Họ tên", field: "hoTen", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Ngày sinh", field: "ngaySinh", headerFilter: true, headerVertical: false, editor: dateEditor, formatter: formatDate },
                {
                    title: "Giới tính", field: "gioiTinh", headerFilter: true, headerVertical: false, editor: "list",
                    editorParams: {
                        values: Object.entries(gioi_tinh).map(([key, label]) => ({ value: key,label: `${key} - ${label}`})),
                    },
                    formatter: formatKeyValue(gioi_tinh),
                },
                { title: "Mã Quốc tịch", field: "maQuocTich", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã dân tộc", field: "maDanToc", headerFilter: true, headerVertical: false,
                    editor: "list",
                    editorParams: {
                        // Sử dụng dữ liệu đã load sẵn
                        values: Object.entries(ma_dan_toc).map(([key, label]) => ({ value: key,label: `${key} - ${label}`})),
                        placeholderEmpty:"Không có kết quả", //set custom placeholder when list is empty
                        autocomplete:true, //enable autocomplete mode,
                        listOnEmpty:true, //show all values in the list if the input is empty
                        filterDelay:100, //delay in milliseconds after typing before filter begins
                        clearable:true, //show clear "x" button on editor
                    },
                    // Thêm formatter để hiển thị tên đầy đủ cả mã và tên
                    formatter: formatKeyValue(ma_dan_toc),
                },
                { title: "Mã nghề nghiệp", field: "maNgheNghiep", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Địa chỉ", field: "diaChi", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã Thẻ BHYT", field: "maTheBHYT", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Giá trị thẻ đến", field: "gtTheDen", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Ngày vào", field: "ngayVao", headerFilter: true, headerVertical: false, editor: dateEditor, formatter: formatDate },
                { title: "Ngày vào nội trú", field: "ngayVaoNoiTru", headerFilter: true, headerVertical: false, editor: dateEditor, formatter: formatDate },
                { title: "Ngày ra", field: "ngayRa", headerFilter: true, headerVertical: false, editor: dateEditor, formatter: formatDate },
                { title: "Dấu hiệu lâm sàng", field: "dauHieuLS", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Chẩn đoán ra viện", field: "chanDoanRV", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Quá trình bệnh lý", field: "qtBenhLy", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Tóm tắt kết quả", field: "tomtatKQ", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Phương pháp điều trị", field: "ppDieuTri", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã bệnh chính", field: "maBenhChinh", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã bệnh kèm theo", field: "maBenhKT", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã bệnh YHCT", field: "maBenhYHCT", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã loại ra viện", field: "maLoaiRV", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã lý do chuyển", field: "maLyDoCT", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Hướng điều trị", field: "huongDieuTri", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Phương tiện vận chuyển", field: "phuongTienVC", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Họ tên người hộ tống", field: "hoTenNguoiHT", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Chức danh người hộ tống", field: "chucDanhNguoiHT", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã Thủ trưởng đơn vị", field: "maTTDV", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã Bác sĩ", field: "maBS", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Dự phòng", field: "maNgheNghiep", headerFilter: true, headerVertical: false, editor:"input" }
            ]);
            break;

        case 'XML14':
            columns = columns.concat([
                { title:"Chọn", formatter:"rowSelection", titleFormatter:"rowSelection", hozAlign:"center", headerSort:false, frozen:true, width:40},
                { title: "Mã liên kết", field: "maLK", headerFilter: true, headerVertical: false, frozen:true },
                { title: "Số giấy hẹn khám lại", field: "soGiayHenKL", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã cơ sở KCB", field: "maCSKCB", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Họ tên", field: "hoTen", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Ngày sinh", field: "ngaySinh", headerFilter: true, headerVertical: false, editor: dateEditor, formatter: formatDate },
                {
                    title: "Giới tính", field: "gioiTinh", headerFilter: true, headerVertical: false, editor: "list",
                    editorParams: {
                        values: Object.entries(gioi_tinh).map(([key, label]) => ({ value: key,label: `${key} - ${label}`})),
                    },
                    formatter: formatKeyValue(gioi_tinh),
                },
                { title: "Địa chỉ", field: "diaChi", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã thẻ BHYT", field: "maTheBHYT", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Giá trị thẻ đến", field: "gtTheDen", headerFilter: true, headerVertical: false, editor: dateEditor, formatter: formatDate },
                { title: "Ngày vào", field: "ngayVao", headerFilter: true, headerVertical: false, editor: dateEditor, formatter: formatDate },
                { title: "Ngày vào nội trú", field: "ngayVaoNoiTru", headerFilter: true, headerVertical: false, editor: dateEditor, formatter: formatDate },
                { title: "Ngày ra", field: "ngayRa", headerFilter: true, headerVertical: false, editor: dateEditor, formatter: formatDate },
                { title: "Ngày hẹn khám lại", field: "ngayHenKL", headerFilter: true, headerVertical: false, editor: dateEditor, formatter: formatDate },
                { title: "Chẩn đoán ra viện", field: "chanDoanRV", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã bệnh chính", field: "maBenhChinh", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã bệnh kèm theo", field: "maBenhKT", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã bệnh YHCT", field: "maBenhYHCT", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã đối tượng KCB", field: "maDoiTuongKCB", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã Bác sĩ", field: "maBacSi", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã Thủ trưởng đơn vị", field: "maTTDV", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Ngày chứng từ", field: "ngayCT", headerFilter: true, headerVertical: false, editor: dateEditor, formatter: formatDate },
                { title: "Dự phòng", field: "duPhong", headerFilter: true, headerVertical: false, editor:"input" }
            ]);
            break;

        case 'XML15':
            columns = columns.concat([
                { title:"Chọn", formatter:"rowSelection", titleFormatter:"rowSelection", hozAlign:"center", headerSort:false, frozen:true, width:40},
                { title: "Mã liên kết", field: "maLK", headerFilter: true, headerVertical: false, frozen:true },
                { title: "Mã bệnh nhân", field: "maBN", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Họ tên", field: "hoTen", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Số CCCD", field: "soCCCD", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Phân loại lao vị trí", field: "phanLoaiLaoViTri", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Phân loại lao tiền sử", field: "phanLoaiLaoTS", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Phân loại lao HIV", field: "phanLoaiLaoHIV", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Phân loại lao vi khuẩn", field: "phanLoaiLaoVK", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Phân loại lao kèm theo", field: "phanLoaiLaoKT", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Loại điều trị lao", field: "loaiDTriLao", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Phác đồ điều trị lao", field: "phacDoDTriLao", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Ngày bắt đầu điều trị lao", field: "ngayBDDTriLao", headerFilter: true, headerVertical: false, editor: dateEditor, formatter: formatDate },
                { title: "Phác đồ điều trị Lao", field: "phacDoDTriLao", headerFilter: true, headerVertical: false, editor: dateEditor, formatter: formatDate },
                { title: "Ngày kết thúc điều trị lao", field: "ngayKTDTriLao", headerFilter: true, headerVertical: false, editor: dateEditor, formatter: formatDate },
                { title: "Kết quả điều trị lao", field: "ketQuaDTriLao", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Mã CSKCB", field: "maCSKCB", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Ngày khẳng định HIV", field: "ngayKDHIV", headerFilter: true, headerVertical: false, editor: dateEditor, formatter: formatDate },
                { title: "Bắt đầu điều trị ARV", field: "bddtARV", headerFilter: true, headerVertical: false, editor:"input" },
                { title: "Ngày bắt đầu điều trị chất xéa", field: "ngayBatDauDTCTX", headerFilter: true, headerVertical: false, editor: dateEditor, formatter: formatDate },
                { title: "Dự phòng", field: "duPhong", headerFilter: true, headerVertical: false, editor:"input" }
            ]);
            break;
    }

    // Cột thao tác (cột cuối cùng)
    columns.push({
        title: "Thao tác",
        formatter: function(cell, formatterParams, onRendered) {
            var row = cell.getRow();
            var data = row.getData();
            var id = data.id;
            var maLK = data.maLK;
            var type = xmlType;

            var editButton = '';
            if (maLK) {
                editButton = '<a href="/xml4750/edit/' + type + '/' + maLK + '/" class="btn btn-sm btn-primary mr-1" title="Sửa"><i class="fas fa-edit"></i></a>';
            } else {
                editButton = '<button type="button" class="btn btn-sm btn-primary mr-1 disabled" title="Sửa"><i class="fas fa-edit"></i></button>';
            }

            var deleteButton = '<button type="button" class="btn btn-sm btn-danger mr-1 delete-xml" data-id="' + id + '" data-type="' + type + '" title="Xóa"><i class="fas fa-trash"></i></button>';
            var exportButton = '<button type="button" class="btn btn-sm btn-success export-xml" data-id="' + id + '" data-type="' + type + '" title="Xuất XML"><i class="fas fa-file-export"></i></button>';

            return '<div class="btn-group">' + editButton + deleteButton + exportButton + '</div>';
        },
        width: 130,
        headerSort: false,
        hozAlign: "center",
        headerHozAlign: "center",
        headerVertical: false,
        resizable: false,
        cssClass: "action-column",
        frozen:true
    });

    return columns;
}

// Hàm thiết lập Tabulator
function setupTables() {
    // Xử lý tab đang hiển thị
    var $activeTab = $('.tab-pane.active');
    if ($activeTab.length > 0) {
        var tabId = $activeTab.attr('id');
        var xmlType = tabId.toUpperCase();

        // Khởi tạo Tabulator cho tab đang hiển thị
        if ($activeTab.find('.xml-tabulator').length > 0) {
            var tableElement = document.getElementById(tabId + '-table');

            // Kiểm tra xem Tabulator đã được khởi tạo chưa
            if (!tableElement._tabulator) {
                var columns = getColumnsConfig(xmlType);

                // Khởi tạo Tabulator mới
                var table = new Tabulator(tableElement, {
                    columns: columns,
                    layout: "fitDataFill",
                    pagination:true, //enable pagination
                    paginationMode: "remote", // Sử dụng phân trang từ xa
                    paginationSize: 20,
                    paginationSizeSelector:[10, 20, 50, 100],
                    ajaxURL: "/xml4750/api/xml-data/", // Sử dụng API endpoint mới
                    ajaxParams: {
                        xml_type: xmlType
                    },
                    ajaxConfig: "GET",
                    ajaxResponse: function(url, params, response) {
                        // Xử lý dữ liệu trả về từ API
                        return {
                            last_page: response.pagination.last_page,
                            data: response.data
                        };
                    },
                    scrollX: true,
                    autoResize: true,
                    movableColumns: true,
                    selectable: true,
                    height: "calc(100vh - 400px)",
                    placeholder: "Không có dữ liệu",
                    tooltips: true,
                    headerFilterLiveFilterDelay: 300,
                    resizableColumnFit: true,
                    fixedHeader: true, // Fix the header
                    cellEdited:function(cell){
                        var rowData = cell.getRow().getData();
                        console.log("Updated " + xmlType + " row:", rowData);
                        // Gửi AJAX cập nhật lên server
                        saveEditedCell(cell);
                    },
                    langs: {
                        "vi-vn": {
                            "columns": {
                                "name": "Tên",
                                "progress": "Tiến độ",
                                "gender": "Giới tính",
                                "rating": "Đánh giá",
                                "col": "Cột"
                            },
                            "ajax": {
                                "loading": "Đang tải",
                                "error": "Lỗi"
                            },
                            "pagination": {
                                "page_size": "Kích thước trang",
                                "page_title": "Hiển thị trang",
                                "first": "Đầu",
                                "first_title": "Trang đầu",
                                "last": "Cuối",
                                "last_title": "Trang cuối",
                                "prev": "Trước",
                                "prev_title": "Trang trước",
                                "next": "Tiếp",
                                "next_title": "Trang tiếp theo"
                            },
                            "headerFilters": {
                                "default": "Tìm kiếm...",
                            }
                        }
                    }
                });
            }
        }
    }
}
