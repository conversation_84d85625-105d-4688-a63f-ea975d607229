# Generated by Django 4.2.7 on 2025-05-24 15:03

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('xml4750', '0011_alter_xml10model_options_alter_xml11model_options_and_more'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='xml13model',
            options={'verbose_name': 'Chỉ tiêu dữ liệu giấy chuyển tuyến/chuyển cơ sở khám bệnh, chữa bệnh bảo hiểm y tế', 'verbose_name_plural': 'Chỉ tiêu dữ liệu giấy chuyển tuyến/chuyển cơ sở khám bệnh, chữa bệnh bảo hiểm y tế'},
        ),
        migrations.AlterModelOptions(
            name='xml14model',
            options={'verbose_name': ' Chỉ tiêu dữ liệu giấy hẹn khám lại', 'verbose_name_plural': ' Chỉ tiêu dữ liệu giấy hẹn khám lại'},
        ),
        migrations.AlterModelOptions(
            name='xml15model',
            options={'verbose_name': 'Chỉ tiêu thông tin quản lý điều trị bệnh lao', 'verbose_name_plural': 'Chỉ tiêu thông tin quản lý điều trị bệnh lao'},
        ),
        migrations.RemoveField(
            model_name='xml13model',
            name='donGiaBH',
        ),
        migrations.RemoveField(
            model_name='xml13model',
            name='donGiaBV',
        ),
        migrations.RemoveField(
            model_name='xml13model',
            name='donViTinh',
        ),
        migrations.RemoveField(
            model_name='xml13model',
            name='duongDung',
        ),
        migrations.RemoveField(
            model_name='xml13model',
            name='hamLuong',
        ),
        migrations.RemoveField(
            model_name='xml13model',
            name='lieuDung',
        ),
        migrations.RemoveField(
            model_name='xml13model',
            name='maBenh',
        ),
        migrations.RemoveField(
            model_name='xml13model',
            name='maKhoa',
        ),
        migrations.RemoveField(
            model_name='xml13model',
            name='maNhom',
        ),
        migrations.RemoveField(
            model_name='xml13model',
            name='maPTTT',
        ),
        migrations.RemoveField(
            model_name='xml13model',
            name='maThuoc',
        ),
        migrations.RemoveField(
            model_name='xml13model',
            name='mucHuong',
        ),
        migrations.RemoveField(
            model_name='xml13model',
            name='ngayDung',
        ),
        migrations.RemoveField(
            model_name='xml13model',
            name='ngayYL',
        ),
        migrations.RemoveField(
            model_name='xml13model',
            name='nguoiThucHien',
        ),
        migrations.RemoveField(
            model_name='xml13model',
            name='soLuong',
        ),
        migrations.RemoveField(
            model_name='xml13model',
            name='stt',
        ),
        migrations.RemoveField(
            model_name='xml13model',
            name='tBHTT',
        ),
        migrations.RemoveField(
            model_name='xml13model',
            name='tBNCCT',
        ),
        migrations.RemoveField(
            model_name='xml13model',
            name='tBNTT',
        ),
        migrations.RemoveField(
            model_name='xml13model',
            name='tNguonKhac',
        ),
        migrations.RemoveField(
            model_name='xml13model',
            name='tNguonKhacCL',
        ),
        migrations.RemoveField(
            model_name='xml13model',
            name='tNguonKhacNSNN',
        ),
        migrations.RemoveField(
            model_name='xml13model',
            name='tNguonKhacVTNN',
        ),
        migrations.RemoveField(
            model_name='xml13model',
            name='tNguonKhacVTTN',
        ),
        migrations.RemoveField(
            model_name='xml13model',
            name='tTranTT',
        ),
        migrations.RemoveField(
            model_name='xml13model',
            name='tenThuoc',
        ),
        migrations.RemoveField(
            model_name='xml13model',
            name='thanhTienBH',
        ),
        migrations.RemoveField(
            model_name='xml13model',
            name='thanhTienBV',
        ),
        migrations.RemoveField(
            model_name='xml13model',
            name='ttThau',
        ),
        migrations.RemoveField(
            model_name='xml13model',
            name='tyLeTT',
        ),
        migrations.RemoveField(
            model_name='xml13model',
            name='tyLeTTBH',
        ),
        migrations.RemoveField(
            model_name='xml14model',
            name='giayChuyenTuyen',
        ),
        migrations.RemoveField(
            model_name='xml14model',
            name='huongDieuTri',
        ),
        migrations.RemoveField(
            model_name='xml14model',
            name='maNoiDen',
        ),
        migrations.RemoveField(
            model_name='xml14model',
            name='maNoiDi',
        ),
        migrations.RemoveField(
            model_name='xml14model',
            name='maTheTam',
        ),
        migrations.RemoveField(
            model_name='xml14model',
            name='qtBenhLy',
        ),
        migrations.RemoveField(
            model_name='xml14model',
            name='soChuyenTuyen',
        ),
        migrations.RemoveField(
            model_name='xml14model',
            name='soHoSo',
        ),
        migrations.RemoveField(
            model_name='xml15model',
            name='chanDoan',
        ),
        migrations.RemoveField(
            model_name='xml15model',
            name='diaChi',
        ),
        migrations.RemoveField(
            model_name='xml15model',
            name='ghiChu',
        ),
        migrations.RemoveField(
            model_name='xml15model',
            name='gioiTinh',
        ),
        migrations.RemoveField(
            model_name='xml15model',
            name='maBS',
        ),
        migrations.RemoveField(
            model_name='xml15model',
            name='maTTDV',
        ),
        migrations.RemoveField(
            model_name='xml15model',
            name='maTheBHYT',
        ),
        migrations.RemoveField(
            model_name='xml15model',
            name='maTheTam',
        ),
        migrations.RemoveField(
            model_name='xml15model',
            name='ngayHenKham',
        ),
        migrations.RemoveField(
            model_name='xml15model',
            name='ngaySinh',
        ),
        migrations.RemoveField(
            model_name='xml15model',
            name='ppDieuTri',
        ),
        migrations.RemoveField(
            model_name='xml15model',
            name='soPhieu',
        ),
        migrations.AddField(
            model_name='xml13model',
            name='chanDoanRV',
            field=models.TextField(blank=True, null=True, verbose_name='Chẩn đoán ra viện'),
        ),
        migrations.AddField(
            model_name='xml13model',
            name='chucDanhNguoiHT',
            field=models.TextField(blank=True, null=True, verbose_name='Chức danh người hướng dẫn'),
        ),
        migrations.AddField(
            model_name='xml13model',
            name='dauHieuLS',
            field=models.TextField(blank=True, null=True, verbose_name='Dấu hiệu lâm sàng'),
        ),
        migrations.AddField(
            model_name='xml13model',
            name='diaChi',
            field=models.CharField(blank=True, max_length=1024, null=True, verbose_name='Địa chỉ'),
        ),
        migrations.AddField(
            model_name='xml13model',
            name='giayChuyenTuyen',
            field=models.CharField(blank=True, max_length=200, null=True, verbose_name='Giấy chuyển tuyến'),
        ),
        migrations.AddField(
            model_name='xml13model',
            name='gioiTinh',
            field=models.IntegerField(blank=True, null=True, verbose_name='Giới tính (1: Nam, 2: Nữ)'),
        ),
        migrations.AddField(
            model_name='xml13model',
            name='gtTheDen',
            field=models.CharField(blank=True, max_length=8, null=True, verbose_name='Giá trị thẻ đến (yyyyMMdd)'),
        ),
        migrations.AddField(
            model_name='xml13model',
            name='hoTen',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='Họ tên'),
        ),
        migrations.AddField(
            model_name='xml13model',
            name='hoTenNguoiHT',
            field=models.TextField(blank=True, null=True, verbose_name='Họ tên người hướng dẫn'),
        ),
        migrations.AddField(
            model_name='xml13model',
            name='huongDieuTri',
            field=models.TextField(blank=True, null=True, verbose_name='Hướng điều trị'),
        ),
        migrations.AddField(
            model_name='xml13model',
            name='maBenhChinh',
            field=models.CharField(blank=True, max_length=10, null=True, verbose_name='Mã bệnh chính'),
        ),
        migrations.AddField(
            model_name='xml13model',
            name='maBenhKT',
            field=models.CharField(blank=True, max_length=10, null=True, verbose_name='Mã bệnh kèm theo'),
        ),
        migrations.AddField(
            model_name='xml13model',
            name='maCSKCB',
            field=models.CharField(blank=True, max_length=5, null=True, verbose_name='Mã cơ sở KCB'),
        ),
        migrations.AddField(
            model_name='xml13model',
            name='maDanToc',
            field=models.CharField(blank=True, max_length=10, null=True, verbose_name='Mã dân tộc'),
        ),
        migrations.AddField(
            model_name='xml13model',
            name='maLoaiRV',
            field=models.IntegerField(blank=True, null=True, verbose_name='Mã loại ra viện'),
        ),
        migrations.AddField(
            model_name='xml13model',
            name='maLyDoCT',
            field=models.IntegerField(blank=True, null=True, verbose_name='Mã lý do chuyển'),
        ),
        migrations.AddField(
            model_name='xml13model',
            name='maNgheNghiep',
            field=models.CharField(blank=True, max_length=10, null=True, verbose_name='Mã nghề nghiệp'),
        ),
        migrations.AddField(
            model_name='xml13model',
            name='maNoiDen',
            field=models.CharField(blank=True, max_length=5, null=True, verbose_name='Mã nơi đến'),
        ),
        migrations.AddField(
            model_name='xml13model',
            name='maNoiDi',
            field=models.CharField(blank=True, max_length=5, null=True, verbose_name='Mã nơi đi'),
        ),
        migrations.AddField(
            model_name='xml13model',
            name='maQuocTich',
            field=models.CharField(blank=True, max_length=10, null=True, verbose_name='Mã quốc tịch'),
        ),
        migrations.AddField(
            model_name='xml13model',
            name='maTTDV',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='Mã Thủ trưởng đơn vị'),
        ),
        migrations.AddField(
            model_name='xml13model',
            name='maTheBHYT',
            field=models.CharField(blank=True, max_length=20, null=True, verbose_name='Mã thẻ BHYT'),
        ),
        migrations.AddField(
            model_name='xml13model',
            name='ngayRa',
            field=models.CharField(blank=True, max_length=8, null=True, verbose_name='Ngày ra (yyyyMMdd)'),
        ),
        migrations.AddField(
            model_name='xml13model',
            name='ngaySinh',
            field=models.CharField(blank=True, max_length=8, null=True, verbose_name='Ngày sinh (yyyyMMdd)'),
        ),
        migrations.AddField(
            model_name='xml13model',
            name='ngayVao',
            field=models.CharField(blank=True, max_length=8, null=True, verbose_name='Ngày vào (yyyyMMdd)'),
        ),
        migrations.AddField(
            model_name='xml13model',
            name='ngayVaoNoiTru',
            field=models.CharField(blank=True, max_length=8, null=True, verbose_name='Ngày vào nội trú (yyyyMMdd)'),
        ),
        migrations.AddField(
            model_name='xml13model',
            name='phuongTienVC',
            field=models.TextField(blank=True, null=True, verbose_name='Phương tiện vận chuyển'),
        ),
        migrations.AddField(
            model_name='xml13model',
            name='ppDieuTri',
            field=models.TextField(blank=True, null=True, verbose_name='Phương pháp điều trị'),
        ),
        migrations.AddField(
            model_name='xml13model',
            name='qtBenhLy',
            field=models.TextField(blank=True, null=True, verbose_name='Quá trình bệnh lý'),
        ),
        migrations.AddField(
            model_name='xml13model',
            name='soChuyenTuyen',
            field=models.CharField(blank=True, max_length=200, null=True, verbose_name='Số chuyển tuyến'),
        ),
        migrations.AddField(
            model_name='xml13model',
            name='soHoSo',
            field=models.CharField(blank=True, max_length=200, null=True, verbose_name='Số hồ sơ'),
        ),
        migrations.AddField(
            model_name='xml13model',
            name='tomtatKQ',
            field=models.TextField(blank=True, null=True, verbose_name='Tóm tắt kết quả'),
        ),
        migrations.AddField(
            model_name='xml14model',
            name='diaChi',
            field=models.CharField(blank=True, max_length=1024, null=True, verbose_name='Địa chỉ'),
        ),
        migrations.AddField(
            model_name='xml14model',
            name='maBacSi',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='Mã bác sĩ'),
        ),
        migrations.AddField(
            model_name='xml14model',
            name='maBenhChinh',
            field=models.CharField(blank=True, max_length=10, null=True, verbose_name='Mã bệnh chính'),
        ),
        migrations.AddField(
            model_name='xml14model',
            name='maBenhKT',
            field=models.CharField(blank=True, max_length=10, null=True, verbose_name='Mã bệnh kèm theo'),
        ),
        migrations.AddField(
            model_name='xml14model',
            name='maBenhYHCT',
            field=models.CharField(blank=True, max_length=10, null=True, verbose_name='Mã bệnh y học cổ truyền'),
        ),
        migrations.AddField(
            model_name='xml14model',
            name='maDoiTuongKCB',
            field=models.CharField(blank=True, max_length=4, null=True, verbose_name='Mã đối tượng KCB'),
        ),
        migrations.AddField(
            model_name='xml14model',
            name='ngayHenKL',
            field=models.CharField(blank=True, max_length=8, null=True, verbose_name='Ngày hẹn khám lại (yyyyMMdd)'),
        ),
        migrations.AddField(
            model_name='xml14model',
            name='ngayRa',
            field=models.CharField(blank=True, max_length=8, null=True, verbose_name='Ngày ra (yyyyMMdd)'),
        ),
        migrations.AddField(
            model_name='xml14model',
            name='ngayVao',
            field=models.CharField(blank=True, max_length=8, null=True, verbose_name='Ngày vào (yyyyMMdd)'),
        ),
        migrations.AddField(
            model_name='xml14model',
            name='ngayVaoNoiTru',
            field=models.CharField(blank=True, max_length=8, null=True, verbose_name='Ngày vào nội trú (yyyyMMdd)'),
        ),
        migrations.AddField(
            model_name='xml14model',
            name='soGiayHenKL',
            field=models.CharField(blank=True, max_length=200, null=True, verbose_name='Số giấy hẹn khám lại'),
        ),
        migrations.AddField(
            model_name='xml15model',
            name='bddtARV',
            field=models.CharField(blank=True, max_length=8, null=True, verbose_name='Bắt đầu điều trị ARV (yyyyMMdd)'),
        ),
        migrations.AddField(
            model_name='xml15model',
            name='ketQuaDTriLao',
            field=models.IntegerField(blank=True, null=True, verbose_name='Kết quả điều trị lao'),
        ),
        migrations.AddField(
            model_name='xml15model',
            name='loaiDTriLao',
            field=models.IntegerField(blank=True, null=True, verbose_name='Loại điều trị lao'),
        ),
        migrations.AddField(
            model_name='xml15model',
            name='maBN',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='Mã bệnh nhân'),
        ),
        migrations.AddField(
            model_name='xml15model',
            name='maCSKCB',
            field=models.CharField(blank=True, max_length=4, null=True, verbose_name='Mã cơ sở KCB'),
        ),
        migrations.AddField(
            model_name='xml15model',
            name='ngayBDDTriLao',
            field=models.CharField(blank=True, max_length=8, null=True, verbose_name='Ngày bắt đầu điều trị lao (yyyyMMdd)'),
        ),
        migrations.AddField(
            model_name='xml15model',
            name='ngayBatDauDTCTX',
            field=models.CharField(blank=True, max_length=8, null=True, verbose_name='Ngày bắt đầu điều trị chất xéa (yyyyMMdd)'),
        ),
        migrations.AddField(
            model_name='xml15model',
            name='ngayKDHIV',
            field=models.CharField(blank=True, max_length=8, null=True, verbose_name='Ngày kết thúc điều trị HIV (yyyyMMdd)'),
        ),
        migrations.AddField(
            model_name='xml15model',
            name='ngayKTDTriLao',
            field=models.CharField(blank=True, max_length=8, null=True, verbose_name='Ngày kết thúc điều trị lao (yyyyMMdd)'),
        ),
        migrations.AddField(
            model_name='xml15model',
            name='phacDoDTriLao',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='Phác đồ điều trị lao'),
        ),
        migrations.AddField(
            model_name='xml15model',
            name='phanLoaiLaoHIV',
            field=models.IntegerField(blank=True, null=True, verbose_name='Phân loại lao HIV'),
        ),
        migrations.AddField(
            model_name='xml15model',
            name='phanLoaiLaoKT',
            field=models.IntegerField(blank=True, null=True, verbose_name='Phân loại lao kèm theo'),
        ),
        migrations.AddField(
            model_name='xml15model',
            name='phanLoaiLaoTS',
            field=models.IntegerField(blank=True, null=True, verbose_name='Phân loại lao tiền sử'),
        ),
        migrations.AddField(
            model_name='xml15model',
            name='phanLoaiLaoVK',
            field=models.IntegerField(blank=True, null=True, verbose_name='Phân loại lao vi khuẩn'),
        ),
        migrations.AddField(
            model_name='xml15model',
            name='phanLoaiLaoViTri',
            field=models.IntegerField(blank=True, null=True, verbose_name='Phân loại lao vị trí'),
        ),
        migrations.AddField(
            model_name='xml15model',
            name='soCCCD',
            field=models.CharField(blank=True, max_length=20, null=True, verbose_name='Số CCCD'),
        ),
        migrations.AlterField(
            model_name='xml13model',
            name='maBacSi',
            field=models.CharField(blank=True, max_length=10, null=True, verbose_name='Mã bác sĩ'),
        ),
        migrations.AlterField(
            model_name='xml13model',
            name='maBenhYHCT',
            field=models.CharField(blank=True, max_length=10, null=True, verbose_name='Mã bệnh y học cổ truyền'),
        ),
    ]
