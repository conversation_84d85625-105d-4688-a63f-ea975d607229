from django.shortcuts import render, redirect, get_object_or_404
from django.http import HttpResponse, JsonResponse
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.db.models import Q
from django.core.paginator import Paginator
from .forms import XML_FORM_MAP

from .models import (
    XML0Model, XML1Model, XML2Model, XML3Model, XML4Model, XML5Model,
    XML6Model, XML7Model, XML8Model, XML9Model, XML10Model, XML11Model,
    XML12Model, XML13Model, XML14Model, XML15Model
)

# Dictionary to map XML type to model
XML_MODEL_MAP = {
    'XML0': XML0Model,
    'XML1': XML1Model,
    'XML2': XML2Model,
    'XML3': XML3Model,
    'XML4': XML4Model,
    'XML5': XML5Model,
    'XML6': XML6Model,
    'XML7': XML7Model,
    'XML8': XML8Model,
    'XML9': XML9Model,
    'XML10': XML10Model,
    'XML11': XML11Model,
    'XML12': XML12Model,
    'XML13': XML13Model,
    'XML14': XML14Model,
    'XML15': XML15Model,
}

@login_required
def index(request):
    """
    Redirect to list view
    """
    # Chuyển hướng đến trang danh sách XML
    return redirect('xml4750:list_xml')

@login_required
def list_xml(request):
    """
    Display list of all XML records
    """
    # Get search parameters
    search_query = request.GET.get('search', '')
    xml_type = request.GET.get('xml_type', '')

    # Initialize data dictionary
    data = {
        'xml0_list': [],
        'xml1_list': [],
        'xml2_list': [],
        'xml3_list': [],
        'xml4_list': [],
        'xml5_list': [],
        'xml6_list': [],
        'xml7_list': [],
        'xml8_list': [],
        'xml9_list': [],
        'xml10_list': [],
        'xml11_list': [],
        'xml12_list': [],
        'xml13_list': [],
        'xml14_list': [],
        'xml15_list': [],
    }

    # Query data based on search parameters
    for xml_key, model in XML_MODEL_MAP.items():
        if not xml_type or xml_type == xml_key:
            # Sắp xếp theo ID để tránh cảnh báo UnorderedObjectListWarning
            queryset = model.objects.all().order_by('id')

            # Apply search filter if provided
            if search_query:
                # Create a Q object for each field that should be searchable
                q_objects = Q()
                for field in model._meta.fields:
                    if field.get_internal_type() in ['CharField', 'TextField']:
                        q_objects |= Q(**{f"{field.name}__icontains": search_query})

                queryset = queryset.filter(q_objects)

            # Phân trang ở phía server
            paginator = Paginator(queryset, 20)  # Tăng số lượng mục trên mỗi trang lên 20
            page_number = request.GET.get(f'page_{xml_key.lower()}', 1)
            page_obj = paginator.get_page(page_number)

            # Add to data dictionary - chuyển page_obj thành list
            data[f'{xml_key.lower()}_list'] = list(page_obj.object_list)

            # Thêm thông tin phân trang để sử dụng trong JavaScript
            data[f'{xml_key.lower()}_pagination'] = {
                'total': paginator.count,
                'per_page': paginator.per_page,
                'current_page': page_obj.number,
                'last_page': paginator.num_pages,
                'has_next': page_obj.has_next(),
                'has_previous': page_obj.has_previous(),
            }

    # Add search parameters to context
    data['search_query'] = search_query
    data['xml_type'] = xml_type

    return render(request, 'xml4750/list_4750.html', data)

@login_required
def edit_xml(request, xml_type, maLK):
    """
    Edit XML record
    """
    # Get model based on XML type
    model = XML_MODEL_MAP.get(xml_type.upper())
    if not model:
        messages.error(request, f"Không tìm thấy loại XML: {xml_type}")
        return redirect('xml4750:list_xml')

    try:
        # Get record - handle multiple records with same maLK
        records = model.objects.filter(maLK=maLK)
        if not records.exists():
            messages.error(request, f"Không tìm thấy bản ghi {xml_type} với mã liên kết: {maLK}")
            return redirect('xml4750:list_xml')

        if records.count() > 1:
            # If multiple records found, use the first one and warn the user
            record = records.first()
            messages.warning(request, f"Tìm thấy {records.count()} bản ghi với mã liên kết {maLK}. Đang chỉnh sửa bản ghi đầu tiên.")
        else:
            record = records.first()

        # Get form class
        form_class = XML_FORM_MAP.get(xml_type.upper())

        # Handle form submission
        if request.method == 'POST':
            if form_class:
                # Use form for validation
                form = form_class(request.POST, instance=record)
                if form.is_valid():
                    form.save()
                    messages.success(request, f"Đã cập nhật {xml_type} thành công!")
                    return redirect('xml4750:list_xml')
                else:
                    messages.error(request, "Vui lòng kiểm tra lại thông tin!")
            else:
                # Fallback to manual update
                for field in model._meta.fields:
                    if field.name in request.POST and field.name not in ['id', 'ngayTao', 'ngayChinhSua', 'trangThaiGuiBHXH']:
                        setattr(record, field.name, request.POST.get(field.name))

                # Save record
                record.save()

                messages.success(request, f"Đã cập nhật {xml_type} thành công!")
                return redirect('xml4750:list_xml')
        else:
            # Create form for GET request
            if form_class:
                form = form_class(instance=record)
            else:
                form = None

        # Prepare context
        context = {
            'record': record,
            'xml_type': xml_type,
            'fields': model._meta.fields,
            'form': form,
        }

        return render(request, 'xml4750/xml_edit_4750.html', context)
    except Exception as e:
        messages.error(request, f"Lỗi khi chỉnh sửa {xml_type}: {str(e)}")
        return redirect('xml4750:list_xml')

@login_required
def import_xml(request):
    """
    Import XML file
    """
    if request.method == 'POST' and request.FILES.get('xml_file'):
        xml_file = request.FILES['xml_file']
        xml_type = request.POST.get('xml_type')

        if not xml_type:
            messages.error(request, "Vui lòng chọn loại XML!")
            return redirect('xml4750:list_xml')

        try:
            # Parse XML file
            from .utils import parse_xml_file, parse_xml_to_model, XML_MODEL_MAP

            # Get model class
            model_class = XML_MODEL_MAP.get(xml_type)
            if not model_class:
                messages.error(request, f"Loại XML không hợp lệ: {xml_type}")
                return redirect('xml4750:list_xml')

            # Parse XML file
            try:
                xml_data = parse_xml_file(xml_file)
            except Exception as e:
                # If parsing fails, try to read the file directly
                xml_file.seek(0)  # Reset file pointer
                xml_content = xml_file.read().decode('utf-8')

                # Fix common XML declaration issues
                if '<?xml' in xml_content:
                    # Fix invalid XML declaration
                    if '<?xml version="encoding=' in xml_content:
                        xml_content = xml_content.replace('<?xml version="encoding=', '<?xml version="1.0" encoding=')

                    # Fix other potential XML declaration issues
                    if not ('<?xml version=' in xml_content and 'encoding=' in xml_content):
                        xml_content = xml_content.replace('<?xml', '<?xml version="1.0" encoding="utf-8"')

                # For XML0 or XML1, try to parse directly
                if xml_type == 'XML0' or xml_type == 'XML1':
                    try:
                        # Check if the XML content contains the required structure
                        if xml_type == 'XML0' and 'CHI_TIEU_TRANG_THAI_KCB' not in xml_content:
                            messages.error(request, "File XML không chứa cấu trúc CHI_TIEU_TRANG_THAI_KCB!")
                            return redirect('xml4750:list_xml')

                        if xml_type == 'XML1' and 'TONG_HOP' not in xml_content:
                            # Kiểm tra xem có phải là dữ liệu Base64 không
                            try:
                                # Thử giải mã Base64
                                from .utils import decode_base64_to_xml
                                decoded_content = decode_base64_to_xml(xml_content.strip())
                                if 'TONG_HOP' in decoded_content:
                                    # Nếu giải mã thành công, sử dụng nội dung đã giải mã
                                    xml_content = decoded_content
                                else:
                                    messages.error(request, "File XML không chứa cấu trúc THONG_TIN_HANH_CHINH hoặc TONG_HOP!")
                                    return redirect('xml4750:list_xml')
                            except Exception:
                                messages.error(request, "File XML không chứa cấu trúc THONG_TIN_HANH_CHINH hoặc TONG_HOP!")
                                return redirect('xml4750:list_xml')

                        model_instance = parse_xml_to_model(xml_type, xml_content)
                        model_instance.save()
                        messages.success(request, f"Đã nhập file XML {xml_type} thành công!")
                        return redirect('xml4750:list_xml')
                    except Exception as parse_error:
                        messages.error(request, f"Lỗi khi phân tích file XML: {str(parse_error)}")
                        return redirect('xml4750:list_xml')
                elif xml_type == 'XML2' and 'CHI_TIET_THUOC' in xml_content:
                    try:
                        # Sử dụng hàm parse_multiple_xml_to_models cho XML2
                        from .utils import parse_multiple_xml_to_models
                        model_instances = parse_multiple_xml_to_models(xml_type, xml_content)

                        if not model_instances:
                            messages.error(request, "Không tìm thấy dữ liệu CHI_TIET_THUOC hợp lệ trong file XML!")
                            return redirect('xml4750:list_xml')

                        # Lưu tất cả các bản ghi
                        saved_count = 0
                        for model_instance in model_instances:
                            # Luôn tạo bản ghi mới với id tự tăng
                            model_instance.save()
                            saved_count += 1

                        messages.success(request, f"Đã thêm mới {saved_count} bản ghi XML {xml_type}!")

                        return redirect('xml4750:list_xml')
                    except Exception as parse_error:
                        messages.error(request, f"Lỗi khi phân tích file XML: {str(parse_error)}")
                        return redirect('xml4750:list_xml')
                elif xml_type == 'XML3' and 'CHI_TIET_DVKT' in xml_content:
                    try:
                        # Sử dụng hàm parse_multiple_xml_to_models cho XML3
                        from .utils import parse_multiple_xml_to_models
                        model_instances = parse_multiple_xml_to_models(xml_type, xml_content)

                        if not model_instances:
                            messages.error(request, "Không tìm thấy dữ liệu CHI_TIET_DVKT hợp lệ trong file XML!")
                            return redirect('xml4750:list_xml')

                        # Lưu tất cả các bản ghi
                        saved_count = 0
                        for model_instance in model_instances:
                            # Luôn tạo bản ghi mới với id tự tăng
                            model_instance.save()
                            saved_count += 1

                        messages.success(request, f"Đã thêm mới {saved_count} bản ghi XML {xml_type}!")

                        return redirect('xml4750:list_xml')
                    except Exception as parse_error:
                        messages.error(request, f"Lỗi khi phân tích file XML: {str(parse_error)}")
                        return redirect('xml4750:list_xml')
                elif xml_type == 'XML4' and 'CHI_TIET_CLS' in xml_content:
                    try:
                        # Sử dụng hàm parse_multiple_xml_to_models cho XML4
                        from .utils import parse_multiple_xml_to_models
                        model_instances = parse_multiple_xml_to_models(xml_type, xml_content)

                        if not model_instances:
                            messages.error(request, "Không tìm thấy dữ liệu CHI_TIET_CLS hợp lệ trong file XML!")
                            return redirect('xml4750:list_xml')

                        # Lưu tất cả các bản ghi
                        saved_count = 0
                        for model_instance in model_instances:
                            # Luôn tạo bản ghi mới với id tự tăng
                            model_instance.save()
                            saved_count += 1

                        messages.success(request, f"Đã thêm mới {saved_count} bản ghi XML {xml_type}!")

                        return redirect('xml4750:list_xml')
                    except Exception as parse_error:
                        messages.error(request, f"Lỗi khi phân tích file XML: {str(parse_error)}")
                        return redirect('xml4750:list_xml')
                elif xml_type == 'XML5' and 'CHI_TIET_DIEN_BIEN_BENH' in xml_content:
                    try:
                        # Sử dụng hàm parse_multiple_xml_to_models cho XML5
                        from .utils import parse_multiple_xml_to_models
                        model_instances = parse_multiple_xml_to_models(xml_type, xml_content)

                        if not model_instances:
                            messages.error(request, "Không tìm thấy dữ liệu CHI_TIET_DIEN_BIEN_BENH hợp lệ trong file XML!")
                            return redirect('xml4750:list_xml')

                        # Lưu tất cả các bản ghi
                        saved_count = 0
                        for model_instance in model_instances:
                            # Luôn tạo bản ghi mới với id tự tăng
                            model_instance.save()
                            saved_count += 1

                        messages.success(request, f"Đã thêm mới {saved_count} bản ghi XML {xml_type}!")

                        return redirect('xml4750:list_xml')
                    except Exception as parse_error:
                        messages.error(request, f"Lỗi khi phân tích file XML: {str(parse_error)}")
                        return redirect('xml4750:list_xml')
                elif xml_type == 'XML13' and 'CHI_TIET_THUOC' in xml_content:
                    try:
                        # Sử dụng hàm parse_multiple_xml_to_models cho XML13
                        from .utils import parse_multiple_xml_to_models
                        model_instances = parse_multiple_xml_to_models(xml_type, xml_content)

                        if not model_instances:
                            messages.error(request, "Không tìm thấy dữ liệu CHI_TIET_THUOC hợp lệ trong file XML!")
                            return redirect('xml4750:list_xml')

                        # Lưu tất cả các bản ghi
                        saved_count = 0
                        for model_instance in model_instances:
                            # Luôn tạo bản ghi mới với id tự tăng
                            model_instance.save()
                            saved_count += 1

                        messages.success(request, f"Đã thêm mới {saved_count} bản ghi XML {xml_type}!")

                        return redirect('xml4750:list_xml')
                    except Exception as parse_error:
                        messages.error(request, f"Lỗi khi phân tích file XML: {str(parse_error)}")
                        return redirect('xml4750:list_xml')
                else:
                    messages.error(request, f"Lỗi khi phân tích file XML: {str(e)}")
                    return redirect('xml4750:list_xml')

            # Check if XML type exists in parsed data
            if xml_type not in xml_data:
                messages.error(request, f"Không tìm thấy dữ liệu {xml_type} trong file XML!")
                return redirect('xml4750:list_xml')

            # Parse XML content to model
            xml_content = xml_data[xml_type]

            # Xử lý đặc biệt cho XML có nhiều bản ghi (XML2, XML3, XML4, XML5, XML13)
            if xml_type in ['XML2', 'XML3', 'XML4', 'XML5', 'XML13']:
                from .utils import parse_multiple_xml_to_models
                model_instances = parse_multiple_xml_to_models(xml_type, xml_content)

                if not model_instances:
                    element_names = {
                        'XML2': 'CHI_TIET_THUOC',
                        'XML3': 'CHI_TIET_DVKT',
                        'XML4': 'CHI_TIET_CLS',
                        'XML5': 'CHI_TIET_DIEN_BIEN_BENH',
                        'XML13': 'CHI_TIET_THUOC'
                    }
                    element_name = element_names.get(xml_type, 'dữ liệu')
                    messages.error(request, f"Không tìm thấy dữ liệu {element_name} hợp lệ trong file XML!")
                    return redirect('xml4750:list_xml')

                # Lưu tất cả các bản ghi
                saved_count = 0
                for model_instance in model_instances:
                    # Luôn tạo bản ghi mới với id tự tăng
                    model_instance.save()
                    saved_count += 1

                messages.success(request, f"Đã thêm mới {saved_count} bản ghi XML {xml_type}!")
            else:
                # Xử lý cho các loại XML khác (một bản ghi)
                model_instance = parse_xml_to_model(xml_type, xml_content)

                # Kiểm tra xem đã tồn tại bản ghi với maLK này chưa
                if hasattr(model_instance, 'maLK') and model_instance.maLK:
                    existing_records = model_class.objects.filter(maLK=model_instance.maLK)
                    if existing_records.exists():
                        # Nếu đã tồn tại, cập nhật bản ghi đầu tiên
                        existing_record = existing_records.first()

                        # Cập nhật các trường từ model_instance sang existing_record
                        for field in model_class._meta.fields:
                            if field.name not in ['id', 'ngayTao', 'ngayChinhSua', 'trangThaiGuiBHXH']:
                                value = getattr(model_instance, field.name)
                                if value is not None:
                                    setattr(existing_record, field.name, value)

                        # Lưu bản ghi đã cập nhật
                        existing_record.save()
                        messages.success(request, f"Đã cập nhật bản ghi XML {xml_type} với mã liên kết {model_instance.maLK}!")
                    else:
                        # Nếu chưa tồn tại, lưu bản ghi mới
                        model_instance.save()
                        messages.success(request, f"Đã nhập file XML {xml_type} thành công!")
                else:
                    # Nếu không có maLK, lưu bản ghi mới
                    model_instance.save()
                    messages.success(request, f"Đã nhập file XML {xml_type} thành công!")
        except Exception as e:
            messages.error(request, f"Lỗi khi nhập file XML: {str(e)}")

        return redirect('xml4750:list_xml')

    # If accessed directly, redirect to list view
    return redirect('xml4750:list_xml')

@login_required
def export_xml(request):
    """
    Export XML file
    """
    if request.method == 'POST':
        xml_type = request.POST.get('xml_type')
        maLK = request.POST.get('maLK')

        # Get model based on XML type
        model = XML_MODEL_MAP.get(xml_type.upper())
        if not model:
            messages.error(request, f"Không tìm thấy loại XML: {xml_type}")
            return redirect('xml4750:list_xml')

        try:
            # Get record
            record = get_object_or_404(model, maLK=maLK)

            # Import utility functions
            from .utils import create_xml_file

            # Create XML file
            xml_content = create_xml_file(xml_type, record)

            # Create response
            response = HttpResponse(xml_content, content_type='application/xml')
            response['Content-Disposition'] = f'attachment; filename="{xml_type}_{maLK}.xml"'

            return response
        except Exception as e:
            messages.error(request, f"Lỗi khi xuất file XML: {str(e)}")
            return redirect('xml4750:list_xml')
    elif request.method == 'GET' and request.GET.get('id') and request.GET.get('type'):
        # Handle export from list view
        record_id = request.GET.get('id')
        xml_type = request.GET.get('type')

        # Get model based on XML type
        model = XML_MODEL_MAP.get(xml_type.upper())
        if not model:
            messages.error(request, f"Không tìm thấy loại XML: {xml_type}")
            return redirect('xml4750:list_xml')

        try:
            # Get record
            record = get_object_or_404(model, id=record_id)

            # Import utility functions
            from .utils import create_xml_file

            # Create XML file
            xml_content = create_xml_file(xml_type, record)

            # Create response
            response = HttpResponse(xml_content, content_type='application/xml')
            response['Content-Disposition'] = f'attachment; filename="{xml_type}_{record.maLK}.xml"'

            return response
        except Exception as e:
            messages.error(request, f"Lỗi khi xuất file XML: {str(e)}")
            return redirect('xml4750:list_xml')

    # If accessed directly, redirect to list view
    return redirect('xml4750:list_xml')


@login_required
def delete_xml(request):
    """
    Delete XML record
    """
    if request.method == 'POST':
        xml_type = request.POST.get('type')
        record_id = request.POST.get('id')

        # Get model based on XML type
        model = XML_MODEL_MAP.get(xml_type.upper())
        if not model:
            return JsonResponse({'success': False, 'message': f"Không tìm thấy loại XML: {xml_type}"})

        try:
            # Get record
            record = get_object_or_404(model, id=record_id)

            # Delete record
            record.delete()

            return JsonResponse({'success': True})
        except Exception as e:
            return JsonResponse({'success': False, 'message': str(e)})

    return JsonResponse({'success': False, 'message': 'Phương thức không được hỗ trợ'})

@login_required
def update_field(request):
    """
    Cập nhật một trường dữ liệu từ AJAX request
    """
    if request.method == 'POST':
        xml_type = request.POST.get('xml_type')
        row_id = request.POST.get('row_id')
        field = request.POST.get('field')
        value = request.POST.get('value')

        # Kiểm tra dữ liệu đầu vào
        if not all([xml_type, row_id, field]):
            return JsonResponse({'success': False, 'message': 'Thiếu thông tin cần thiết'})

        # Lấy model dựa trên loại XML
        model = XML_MODEL_MAP.get(xml_type.upper())
        if not model:
            return JsonResponse({'success': False, 'message': f'Không tìm thấy loại XML: {xml_type}'})

        try:
            # Lấy bản ghi
            record = get_object_or_404(model, id=row_id)

            # Kiểm tra xem trường có tồn tại trong model không
            if not hasattr(record, field):
                return JsonResponse({'success': False, 'message': f'Trường {field} không tồn tại trong model {xml_type}'})

            # Xử lý đặc biệt cho trường ngày sinh và giới tính
            if field == 'ngaySinh':
                # Đảm bảo ngày sinh đúng định dạng YYYYMMDDHHmm hoặc YYYYMMDD
                if value and (len(value) == 8 or len(value) == 12):
                    # Đã đúng định dạng YYYYMMDD hoặc YYYYMMDDHHmm
                    pass
                elif value and '/' in value:
                    # Chuyển từ dd/MM/yyyy HH:mm sang YYYYMMDDHHmm
                    try:
                        dateParts = value.split(" ")
                        dateStr = dateParts[0]
                        timeStr = dateParts[1] if len(dateParts) > 1 else "00:00"

                        parts = dateStr.split('/')
                        if len(parts) == 3:
                            day = parts[0].zfill(2)
                            month = parts[1].zfill(2)
                            year = parts[2]

                            timeParts = timeStr.split(':')
                            hour = timeParts[0].zfill(2) if len(timeParts) > 0 else "00"
                            minute = timeParts[1].zfill(2) if len(timeParts) > 1 else "00"

                            value = f"{year}{month}{day}{hour}{minute}"
                        else:
                            return JsonResponse({'success': False, 'message': 'Định dạng ngày sinh không hợp lệ'})
                    except Exception as e:
                        return JsonResponse({'success': False, 'message': f'Lỗi xử lý ngày sinh: {str(e)}'})
            elif field == 'gioiTinh':
                # Chuyển đổi giá trị giới tính sang số
                if value in ['Nam', 'nam', '1', 1]:
                    value = 1
                elif value in ['Nữ', 'nữ', '2', 2]:
                    value = 2
                elif value in ['Chưa xác định', 'chưa xác định', '3', 3]:
                    value = 3
                else:
                    return JsonResponse({'success': False, 'message': 'Giá trị giới tính không hợp lệ'})

            # Cập nhật giá trị
            setattr(record, field, value)
            record.save()

            return JsonResponse({'success': True, 'message': 'Cập nhật thành công'})
        except Exception as e:
            return JsonResponse({'success': False, 'message': f'Lỗi: {str(e)}'})

    return JsonResponse({'success': False, 'message': 'Phương thức không được hỗ trợ'})

@login_required
def list_xml_new(request):
    """
    Trang hiển thị nhiều bảng Tabulator với dữ liệu từ database (XML0 đến XML15)
    """
    context = {}
    # Lấy dữ liệu cho tất cả các loại XML
    for xml_key, model in XML_MODEL_MAP.items():
        try:
            # Lấy tối đa 100 bản ghi để test hiển thị
            queryset = model.objects.all().order_by('id')[:100]
            # Chuyển queryset sang list of dicts để dễ dàng truyền sang JSON trong template
            context[f'{xml_key.lower()}_list'] = list(queryset.values())
        except Exception as e:
            # Log lỗi hoặc xử lý nếu có model nào đó gặp vấn đề
            print(f"Error fetching data for {xml_key}: {e}")
            context[f'{xml_key.lower()}_list'] = [] # Trả về list rỗng nếu lỗi

    # Truyền XML_MODEL_MAP vào context để template có thể lặp qua các loại XML
    context['xml_model_map'] = XML_MODEL_MAP

    return render(request, 'xml4750/list_4750_new.html', context)

@login_required
def api_xml_data(request):
    """
    API endpoint để lấy dữ liệu XML cho Tabulator
    """
    xml_type = request.GET.get('xml_type', '')
    page = request.GET.get('page', 1)
    size = request.GET.get('size', 20)
    search = request.GET.get('search', '')

    try:
        page = int(page)
        size = int(size)
    except ValueError:
        page = 1
        size = 20

    if not xml_type or xml_type not in XML_MODEL_MAP:
        return JsonResponse({
            'data': [],
            'last_page': 1,
            'total': 0
        })

    model = XML_MODEL_MAP.get(xml_type)
    queryset = model.objects.all().order_by('id')

    # Apply search filter if provided
    if search:
        q_objects = Q()
        for field in model._meta.fields:
            if field.get_internal_type() in ['CharField', 'TextField']:
                q_objects |= Q(**{f"{field.name}__icontains": search})
        queryset = queryset.filter(q_objects)

    # Paginate results
    paginator = Paginator(queryset, size)
    page_obj = paginator.get_page(page)

    # Convert queryset to list of dicts
    data = []
    for item in page_obj:
        item_dict = {}
        for field in model._meta.fields:
            value = getattr(item, field.name)
            item_dict[field.name] = str(value) if value is not None else ''
        data.append(item_dict)

    return JsonResponse({
        'data': data,
        'last_page': paginator.num_pages,
        'total': paginator.count
    })

def xml_data_api(request):
    """
    API endpoint để trả về dữ liệu JSON cho Tabulator
    """
    # Lấy parameters từ request
    xml_type = request.GET.get('xml_type', 'xml0')  # Default xml0
    page = int(request.GET.get('page', 1))
    size = int(request.GET.get('size', 20))
    search = request.GET.get('search', '')
    
    # Lấy model tương ứng
    if xml_type not in XML_MODEL_MAP:
        return JsonResponse({'error': 'Invalid xml_type'}, status=400)
    
    model = XML_MODEL_MAP[xml_type]
    
    try:
        # Base queryset
        queryset = model.objects.all().order_by('id')
        
        # Apply search filter
        if search:
            q_objects = Q()
            for field in model._meta.fields:
                if field.get_internal_type() in ['CharField', 'TextField']:
                    q_objects |= Q(**{f"{field.name}__icontains": search})
            queryset = queryset.filter(q_objects)
        
        # Pagination
        paginator = Paginator(queryset, size)
        page_obj = paginator.get_page(page)
        
        # Serialize data - Xử lý multiline fields
        data = []
        for obj in page_obj.object_list:
            item = {}
            for field in model._meta.fields:
                field_name = field.name
                field_value = getattr(obj, field_name)
                
                item[field_name] = field_value
            
            data.append(item)
        
        # Response data
        response_data = {
            'data': data,
            'pagination': {
                'total': paginator.count,
                'per_page': paginator.per_page,
                'current_page': page_obj.number,
                'last_page': paginator.num_pages,
                'has_next': page_obj.has_next(),
                'has_previous': page_obj.has_previous(),
            }
        }
        
        return JsonResponse(response_data, json_dumps_params={'ensure_ascii': False})
        
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)